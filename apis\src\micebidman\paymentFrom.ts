import { download, get, post } from '../request'
import { 
    IPaymentFromFilter, 
    IPaymentFrom,
    IPageResponse, 
    Result } from '@haierbusiness-front/common-libs'


export const paymentFromApi = {
    // 、查询服务商所有的结算单信息
    list: (params: IPaymentFromFilter): Promise<IPageResponse<IPaymentFrom>> => {
        return get('/mice-bid/api/mice/payment/getBillList', params)
    },
    getBillList: (params: IPaymentFromFilter): Promise<IPageResponse<IPaymentFrom>> => {
        return get('/mice-bid/api/mice/payment/getBillList', params)
    },

    get: (id: number): Promise<IPaymentFrom> => {
        return get('merchant/api/paymentFrom/get', {
            id
        })
    },

    save: (params: IPaymentFrom): Promise<Result> => {
        return post('merchant/api/paymentFrom/save', params)
    },

    edit: (params: IPaymentFrom): Promise<Result> => {
        return post('merchant/api/paymentFrom/update', params)
    },

    remove: (id: number): Promise<Result> => {
        return post('merchant/api/paymentFrom/delete', { id })
    },
}
